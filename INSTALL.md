# Guia de Instalação - API de Transcrição de Áudio

## Pré-requisitos

- Python 3.7 ou superior
- pip (gerenciador de pacotes Python)
- Pelo menos 2GB de RAM livre
- Conexão com internet para download dos modelos

## Instalação

### Opção 1: Ambiente Virtual (Recomendado)

1. **<PERSON><PERSON> ou baixe o projeto**
```bash
cd /caminho/para/o/projeto
```

2. **Crie um ambiente virtual**
```bash
python3 -m venv api_venv
```

3. **Ative o ambiente virtual**
```bash
# Linux/Mac
source api_venv/bin/activate

# Windows
api_venv\Scripts\activate
```

4. **Instale as dependências**
```bash
pip install -r requirements.txt
```

5. **Configure as variáveis de ambiente (opcional)**
```bash
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

6. **Execute a API**
```bash
python api.py
```

### Opção 2: Instalação Global (Ubuntu/Debian com ambiente gerenciado)

Se você estiver em um sistema com ambiente Python gerenciado externamente:

1. **Instale python3-venv**
```bash
sudo apt update
sudo apt install python3-venv python3-full
```

2. **Siga os passos da Opção 1**

### Opção 3: Usando pipx (Alternativa)

```bash
# Instale pipx se não tiver
sudo apt install pipx

# Instale as dependências globalmente
pipx install fastapi
pipx install uvicorn[standard]
pipx install python-multipart
pipx install openai-whisper
pipx install pyannote.audio
pipx install python-dotenv
```

## Configuração

### 1. Token HuggingFace (Para identificação de falantes)

1. Crie uma conta em [HuggingFace](https://huggingface.co/)
2. Vá para [Settings > Access Tokens](https://huggingface.co/settings/tokens)
3. Crie um novo token
4. Aceite os termos de uso em [pyannote/speaker-diarization-3.1](https://huggingface.co/pyannote/speaker-diarization-3.1)
5. Adicione o token ao arquivo `.env`:
```
HF_API_KEY=seu_token_aqui
```

### 2. Modelo Whisper

Configure o tamanho do modelo no arquivo `.env`:
```
MODEL_SIZE=small
```

Opções disponíveis:
- `tiny`: ~39 MB, mais rápido, menor precisão
- `base`: ~74 MB, balanceado
- `small`: ~244 MB, boa precisão (padrão)
- `medium`: ~769 MB, alta precisão
- `large`: ~1550 MB, máxima precisão

## Verificação da Instalação

1. **Teste básico**
```bash
python test_api.py
```

2. **Inicie a API**
```bash
python api.py
```

3. **Acesse a documentação**
Abra seu navegador em: http://localhost:8000/docs

## Solução de Problemas

### Erro: "externally-managed-environment"

Este erro ocorre em sistemas Linux modernos (Ubuntu 24+, Debian 12+). Soluções:

1. **Use ambiente virtual (recomendado)**
```bash
python3 -m venv api_venv
source api_venv/bin/activate
pip install -r requirements.txt
```

2. **Instale python3-venv se necessário**
```bash
sudo apt install python3-venv python3-full
```

### Erro: "No module named pip"

```bash
# Ubuntu/Debian
sudo apt install python3-pip

# CentOS/RHEL
sudo yum install python3-pip
```

### Erro de memória durante carregamento do modelo

- Use um modelo menor (tiny ou base)
- Feche outros aplicativos para liberar RAM
- Configure `MODEL_SIZE=tiny` no arquivo `.env`

### Erro de conexão com HuggingFace

1. Verifique sua conexão com internet
2. Confirme que aceitou os termos de uso do modelo
3. Verifique se o token HF_API_KEY está correto

### Erro: "Audio file not found"

- Verifique se o arquivo de áudio existe
- Confirme que o formato é suportado (mp3, mp4, wav, m4a)
- Verifique as permissões do arquivo

## Estrutura de Arquivos

```
projeto/
├── api.py                 # API FastAPI principal
├── main2.py              # Script original (referência)
├── requirements.txt      # Dependências Python
├── .env.example         # Exemplo de configuração
├── .env                 # Suas configurações (criar)
├── README_API.md        # Documentação da API
├── INSTALL.md           # Este arquivo
├── test_api.py          # Script de teste
├── example_client.py    # Exemplos de uso
├── start_api.sh         # Script de inicialização
└── api_venv/            # Ambiente virtual (se criado)
```

## Próximos Passos

Após a instalação bem-sucedida:

1. Leia o [README_API.md](README_API.md) para documentação completa
2. Execute `python example_client.py` para ver exemplos
3. Acesse http://localhost:8000/docs para documentação interativa
4. Teste com seus próprios arquivos de áudio

## Suporte

Se encontrar problemas:

1. Verifique se todas as dependências estão instaladas
2. Confirme que está usando Python 3.7+
3. Teste com o arquivo `test_api.py`
4. Consulte os logs de erro para mais detalhes
