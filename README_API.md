# API de Transcrição de Áudio

Esta API FastAPI fornece funcionalidades de transcrição de áudio usando Whisper e identificação de falantes usando PyAnnote.

## Funcionalidades

- **Transcrição básica**: Converte áudio em texto
- **Transcrição com timestamps**: Inclui marcações de tempo para cada segmento
- **Identificação de falantes**: Identifica quem está falando em cada segmento (requer token HuggingFace)

## Instalação

1. Instale as dependências:
```bash
pip install -r requirements.txt
```

2. Configure as variáveis de ambiente (opcional, crie um arquivo `.env`):
```
HF_API_KEY=seu_token_huggingface_aqui
MODEL_SIZE=small
```

## Executando a API

```bash
python api.py
```

Ou usando uvicorn diretamente:
```bash
uvicorn api:app --host 0.0.0.0 --port 8000 --reload
```

A API estará disponível em: `http://localhost:8000`

## Documentação Interativa

Acesse `http://localhost:8000/docs` para ver a documentação interativa do Swagger.

## Endpoints

### GET `/`
Informações básicas da API

### GET `/health`
Verifica o status da API e dos modelos carregados

### POST `/transcribe`
Transcrição básica de áudio

**Parâmetros:**
- `file`: Arquivo de áudio (mp3, mp4, wav, m4a)
- `language`: Idioma do áudio (padrão: "pt")

**Resposta:**
```json
{
  "text": "Texto transcrito completo",
  "segments": [...],
  "language": "pt"
}
```

### POST `/transcribe-with-timestamps`
Transcrição com marcações de tempo

**Parâmetros:**
- `file`: Arquivo de áudio (mp3, mp4, wav, m4a)
- `language`: Idioma do áudio (padrão: "pt")

**Resposta:**
```json
{
  "text": "Texto transcrito completo",
  "segments": [
    {
      "start": 0.0,
      "end": 5.2,
      "text": "Primeiro segmento de texto"
    }
  ],
  "language": "pt"
}
```

### POST `/transcribe-with-speakers`
Transcrição com identificação de falantes (requer HF_API_KEY)

**Parâmetros:**
- `file`: Arquivo de áudio (mp3, mp4, wav, m4a)
- `language`: Idioma do áudio (padrão: "pt")

**Resposta:**
```json
{
  "segments": [
    {
      "start": 0.0,
      "end": 5.2,
      "speaker": "SPEAKER_00",
      "text": "Primeiro segmento de texto"
    }
  ],
  "language": "pt"
}
```

## Exemplo de Uso com Python

```python
import requests

# Transcrição básica
with open("audio.mp3", "rb") as f:
    files = {"file": f}
    data = {"language": "pt"}
    response = requests.post("http://localhost:8000/transcribe", files=files, data=data)
    result = response.json()
    print(result["text"])
```

## Exemplo de Uso com cURL

```bash
# Transcrição básica
curl -X POST "http://localhost:8000/transcribe" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@audio.mp3" \
     -F "language=pt"

# Transcrição com timestamps
curl -X POST "http://localhost:8000/transcribe-with-timestamps" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@audio.mp3" \
     -F "language=pt"

# Transcrição com falantes (requer HF_API_KEY)
curl -X POST "http://localhost:8000/transcribe-with-speakers" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@audio.mp3" \
     -F "language=pt"
```

## Testando a API

Execute o script de exemplo:
```bash
python example_client.py
```

## Configuração do Token HuggingFace

Para usar a identificação de falantes, você precisa:

1. Criar uma conta no [HuggingFace](https://huggingface.co/)
2. Gerar um token de acesso em [Settings > Access Tokens](https://huggingface.co/settings/tokens)
3. Aceitar os termos de uso do modelo [pyannote/speaker-diarization-3.1](https://huggingface.co/pyannote/speaker-diarization-3.1)
4. Configurar o token no arquivo `.env`:
```
HF_API_KEY=seu_token_aqui
```

## Formatos de Áudio Suportados

- MP3
- MP4
- WAV
- M4A

## Modelos Whisper Disponíveis

- `tiny`: Mais rápido, menor precisão
- `base`: Balanceado
- `small`: Boa precisão (padrão)
- `medium`: Alta precisão
- `large`: Máxima precisão, mais lento

Configure o modelo no arquivo `.env`:
```
MODEL_SIZE=small
```

## Tratamento de Erros

A API retorna códigos de status HTTP apropriados:
- `200`: Sucesso
- `400`: Erro de validação (formato de arquivo inválido, etc.)
- `500`: Erro interno do servidor

## Performance

- O primeiro request pode ser mais lento devido ao carregamento dos modelos
- Requests subsequentes são mais rápidos
- Arquivos maiores levam mais tempo para processar
- A identificação de falantes adiciona tempo de processamento significativo
