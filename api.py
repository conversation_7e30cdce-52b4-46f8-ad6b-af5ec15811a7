import os
import tempfile
from typing import Optional, List
from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import whisper
from pyannote.audio import Pipeline
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = FastAPI(
    title="Audio Transcription API",
    description="API para transcrição de áudio com identificação de falantes usando Whisper e PyAnnote",
    version="1.0.0"
)

class TranscriptionResponse(BaseModel):
    text: str
    segments: Optional[List[dict]] = None
    language: str

class TranscriptionWithSpeakersResponse(BaseModel):
    segments: List[dict]
    language: str

class AudioTranscriber:
    def __init__(self, model_size="small", hf_token=None):
        """
        Initializes the transcriber with a specific model size and optional HuggingFace token.
        Available models: tiny, base, small, medium, large
        """
        print(f"Loading Whisper model '{model_size}'...")
        self.model = whisper.load_model(model_size)
        print("Model loaded successfully!")

        # Initialize speaker diarization pipeline if HF token is provided
        self.diarization_pipeline = None
        if hf_token:
            try:
                print("Loading speaker diarization model...")
                self.diarization_pipeline = Pipeline.from_pretrained(
                    "pyannote/speaker-diarization-3.1",
                    use_auth_token=hf_token
                )
                print("Speaker diarization model loaded successfully!")
            except Exception as e:
                print(f"Warning: Could not load speaker diarization model: {e}")
                print("Speaker identification will not be available.")

    def transcribe_audio(self, audio_path, language="pt"):
        """
        Transcribes the audio file specified by audio_path and returns the full result.
        """
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        print(f"Transcribing audio: {audio_path}")
        result = self.model.transcribe(audio_path, language=language)
        return result

    def transcribe_with_timestamps(self, audio_path, language="pt"):
        """
        Transcribes audio and returns segments with timestamps.
        """
        result = self.transcribe_audio(audio_path, language)
        return result["segments"]

    def diarize_audio(self, audio_path):
        """
        Performs speaker diarization on the audio file.
        Returns a list of (segment, speaker) tuples.
        """
        if not self.diarization_pipeline:
            raise ValueError("Speaker diarization not available. Please provide a HuggingFace token.")

        print("Performing speaker diarization...")
        diarization = self.diarization_pipeline(audio_path)

        # Convert to list of tuples with segment info and speaker
        segments = []
        for segment, _, speaker in diarization.itertracks(yield_label=True):
            segments.append({
                'start': segment.start,
                'end': segment.end,
                'speaker': speaker
            })

        return segments

    def combine_transcription_and_diarization(self, transcription_segments, diarization_segments):
        """
        Combines transcription and diarization results to assign speakers to text segments.
        """
        result = []

        for trans_segment in transcription_segments:
            start_time = trans_segment['start']
            end_time = trans_segment['end']
            text = trans_segment['text'].strip()

            # Find the predominant speaker for this time interval
            speakers = []
            for diar_segment in diarization_segments:
                # Check if there's overlap between transcription and diarization segments
                overlap_start = max(start_time, diar_segment['start'])
                overlap_end = min(end_time, diar_segment['end'])

                if overlap_start < overlap_end:  # There is overlap
                    overlap_duration = overlap_end - overlap_start
                    # Weight by overlap duration
                    for _ in range(int(overlap_duration * 10)):  # 10 samples per second
                        speakers.append(diar_segment['speaker'])

            # Choose the most frequent speaker
            if speakers:
                speaker = max(set(speakers), key=speakers.count)
            else:
                speaker = "UNKNOWN"

            result.append({
                'start': start_time,
                'end': end_time,
                'speaker': speaker,
                'text': text
            })

        return result

    def transcribe_with_speaker_identification(self, audio_path, language="pt"):
        """
        Transcribes audio with speaker identification.
        Returns segments with timestamps, text, and speaker labels.
        """
        if not self.diarization_pipeline:
            print("Warning: Speaker diarization not available. Returning transcription without speaker identification.")
            return self.transcribe_with_timestamps(audio_path, language)

        # Get transcription with timestamps
        transcription_segments = self.transcribe_with_timestamps(audio_path, language)

        # Get speaker diarization
        diarization_segments = self.diarize_audio(audio_path)

        # Combine results
        combined_results = self.combine_transcription_and_diarization(
            transcription_segments, diarization_segments
        )

        return combined_results


# Initialize the transcriber globally
MODEL_SIZE = os.getenv("MODEL_SIZE", "small")
HF_TOKEN = os.getenv("HF_API_KEY")
transcriber = AudioTranscriber(model_size=MODEL_SIZE, hf_token=HF_TOKEN)

@app.get("/")
async def root():
    """
    Endpoint raiz da API
    """
    return {
        "message": "Audio Transcription API",
        "version": "1.0.0",
        "endpoints": {
            "/transcribe": "Transcrição básica de áudio",
            "/transcribe-with-timestamps": "Transcrição com timestamps",
            "/transcribe-with-speakers": "Transcrição com identificação de falantes",
            "/health": "Status da API"
        }
    }

@app.get("/health")
async def health_check():
    """
    Verifica o status da API e dos modelos carregados
    """
    return {
        "status": "healthy",
        "whisper_model": MODEL_SIZE,
        "speaker_diarization": transcriber.diarization_pipeline is not None
    }

@app.post("/transcribe", response_model=TranscriptionResponse)
async def transcribe_audio_endpoint(
    file: UploadFile = File(...),
    language: str = Form("pt")
):
    """
    Transcreve um arquivo de áudio e retorna o texto completo
    """
    if not file.filename.lower().endswith(('.mp3', '.mp4', '.wav', '.m4a')):
        raise HTTPException(status_code=400, detail="Formato de arquivo não suportado. Use: mp3, mp4, wav, m4a")
    
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        # Transcribe audio
        result = transcriber.transcribe_audio(temp_file_path, language=language)
        
        # Clean up temporary file
        os.unlink(temp_file_path)
        
        return TranscriptionResponse(
            text=result["text"],
            segments=result.get("segments"),
            language=language
        )
        
    except Exception as e:
        # Clean up temporary file if it exists
        if 'temp_file_path' in locals():
            try:
                os.unlink(temp_file_path)
            except:
                pass
        raise HTTPException(status_code=500, detail=f"Erro na transcrição: {str(e)}")

@app.post("/transcribe-with-timestamps", response_model=TranscriptionResponse)
async def transcribe_with_timestamps_endpoint(
    file: UploadFile = File(...),
    language: str = Form("pt")
):
    """
    Transcreve um arquivo de áudio e retorna segmentos com timestamps
    """
    if not file.filename.lower().endswith(('.mp3', '.mp4', '.wav', '.m4a')):
        raise HTTPException(status_code=400, detail="Formato de arquivo não suportado. Use: mp3, mp4, wav, m4a")
    
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        # Transcribe audio with timestamps
        segments = transcriber.transcribe_with_timestamps(temp_file_path, language=language)
        
        # Get full text
        full_text = " ".join([segment["text"].strip() for segment in segments])
        
        # Clean up temporary file
        os.unlink(temp_file_path)
        
        return TranscriptionResponse(
            text=full_text,
            segments=segments,
            language=language
        )
        
    except Exception as e:
        # Clean up temporary file if it exists
        if 'temp_file_path' in locals():
            try:
                os.unlink(temp_file_path)
            except:
                pass
        raise HTTPException(status_code=500, detail=f"Erro na transcrição: {str(e)}")

@app.post("/transcribe-with-speakers", response_model=TranscriptionWithSpeakersResponse)
async def transcribe_with_speakers_endpoint(
    file: UploadFile = File(...),
    language: str = Form("pt")
):
    """
    Transcreve um arquivo de áudio com identificação de falantes
    """
    if not transcriber.diarization_pipeline:
        raise HTTPException(
            status_code=400, 
            detail="Identificação de falantes não disponível. Configure HF_API_KEY no arquivo .env"
        )
    
    if not file.filename.lower().endswith(('.mp3', '.mp4', '.wav', '.m4a')):
        raise HTTPException(status_code=400, detail="Formato de arquivo não suportado. Use: mp3, mp4, wav, m4a")
    
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        # Transcribe audio with speaker identification
        segments = transcriber.transcribe_with_speaker_identification(temp_file_path, language=language)
        
        # Clean up temporary file
        os.unlink(temp_file_path)
        
        return TranscriptionWithSpeakersResponse(
            segments=segments,
            language=language
        )
        
    except Exception as e:
        # Clean up temporary file if it exists
        if 'temp_file_path' in locals():
            try:
                os.unlink(temp_file_path)
            except:
                pass
        raise HTTPException(status_code=500, detail=f"Erro na transcrição: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
