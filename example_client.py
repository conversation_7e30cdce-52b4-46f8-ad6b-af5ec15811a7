import requests
import json

# Configuração da API
API_BASE_URL = "http://localhost:8000"

def test_health():
    """Testa o endpoint de health check"""
    response = requests.get(f"{API_BASE_URL}/health")
    print("Health Check:")
    print(json.dumps(response.json(), indent=2))
    print("-" * 50)

def transcribe_basic(audio_file_path, language="pt"):
    """Testa transcrição básica"""
    with open(audio_file_path, "rb") as f:
        files = {"file": f}
        data = {"language": language}
        response = requests.post(f"{API_BASE_URL}/transcribe", files=files, data=data)
    
    if response.status_code == 200:
        result = response.json()
        print("Transcrição Básica:")
        print(f"Texto: {result['text']}")
        print(f"Idioma: {result['language']}")
        print("-" * 50)
        return result
    else:
        print(f"Erro na transcrição básica: {response.status_code}")
        print(response.text)
        return None

def transcribe_with_timestamps(audio_file_path, language="pt"):
    """Testa transcrição com timestamps"""
    with open(audio_file_path, "rb") as f:
        files = {"file": f}
        data = {"language": language}
        response = requests.post(f"{API_BASE_URL}/transcribe-with-timestamps", files=files, data=data)
    
    if response.status_code == 200:
        result = response.json()
        print("Transcrição com Timestamps:")
        print(f"Texto completo: {result['text']}")
        print(f"Idioma: {result['language']}")
        print("\nSegmentos:")
        for segment in result['segments'][:3]:  # Mostra apenas os primeiros 3 segmentos
            print(f"  [{segment['start']:.1f}s - {segment['end']:.1f}s]: {segment['text'].strip()}")
        if len(result['segments']) > 3:
            print(f"  ... e mais {len(result['segments']) - 3} segmentos")
        print("-" * 50)
        return result
    else:
        print(f"Erro na transcrição com timestamps: {response.status_code}")
        print(response.text)
        return None

def transcribe_with_speakers(audio_file_path, language="pt"):
    """Testa transcrição com identificação de falantes"""
    with open(audio_file_path, "rb") as f:
        files = {"file": f}
        data = {"language": language}
        response = requests.post(f"{API_BASE_URL}/transcribe-with-speakers", files=files, data=data)
    
    if response.status_code == 200:
        result = response.json()
        print("Transcrição com Identificação de Falantes:")
        print(f"Idioma: {result['language']}")
        print("\nSegmentos com falantes:")
        for segment in result['segments'][:5]:  # Mostra apenas os primeiros 5 segmentos
            print(f"  [{segment['speaker']} - {segment['start']:.1f}s a {segment['end']:.1f}s]: {segment['text'].strip()}")
        if len(result['segments']) > 5:
            print(f"  ... e mais {len(result['segments']) - 5} segmentos")
        print("-" * 50)
        return result
    else:
        print(f"Erro na transcrição com falantes: {response.status_code}")
        print(response.text)
        return None

def main():
    """Função principal para testar a API"""
    # Caminho para o arquivo de áudio (ajuste conforme necessário)
    audio_file = "audio_debate.mp3"
    
    print("Testando API de Transcrição de Áudio")
    print("=" * 50)
    
    # Teste health check
    test_health()
    
    # Teste transcrição básica
    transcribe_basic(audio_file)
    
    # Teste transcrição com timestamps
    transcribe_with_timestamps(audio_file)
    
    # Teste transcrição com falantes (pode falhar se HF_API_KEY não estiver configurado)
    transcribe_with_speakers(audio_file)

if __name__ == "__main__":
    main()
