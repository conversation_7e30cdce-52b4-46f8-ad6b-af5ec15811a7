import os
import whisper
from pyannote.audio import Pipeline
from dotenv import load_dotenv

class AudioTranscriber:
    def __init__(self, model_size="small", hf_token=None):
        """
        Initializes the transcriber with a specific model size and optional HuggingFace token.
        Available models: tiny, base, small, medium, large
        """
        print(f"Loading Whisper model '{model_size}'...")
        self.model = whisper.load_model(model_size)
        print("Model loaded successfully!")

        # Initialize speaker diarization pipeline if HF token is provided
        self.diarization_pipeline = None
        if hf_token:
            try:
                print("Loading speaker diarization model...")
                self.diarization_pipeline = Pipeline.from_pretrained(
                    "pyannote/speaker-diarization-3.1",
                    use_auth_token=hf_token
                )
                print("Speaker diarization model loaded successfully!")
            except Exception as e:
                print(f"Warning: Could not load speaker diarization model: {e}")
                print("Speaker identification will not be available.")

    def transcribe_audio(self, audio_path, language="pt"):
        """
        Transcribes the audio file specified by audio_path and returns the full result.
        """
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        print(f"Transcribing audio: {audio_path}")
        result = self.model.transcribe(audio_path, language=language)
        return result

    def save_transcription(self, transcription_text, output_file):
        """
        Saves the transcription text to a .txt file.
        """
        with open(output_file, "w", encoding="utf-8") as file:
            file.write(transcription_text)
        print(f"Transcription saved to: {output_file}")

    def transcribe_and_save_from_directory(self, input_dir, output_dir, language="pt"):
        """
        Transcribes all audio files from input directory and saves to output directory.
        """
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Iterate over all files in the input directory
        for file_name in os.listdir(input_dir):
            # Construct the full file path
            audio_path = os.path.join(input_dir, file_name)

            # Check if the file is an audio file
            if os.path.isfile(audio_path) and file_name.lower().endswith(('.mp3', '.mp4', '.wav', '.m4a')):
                # Get the base name of the audio file (without extension)
                base_name = os.path.splitext(file_name)[0]
                output_file = os.path.join(output_dir, f"{base_name}_transcription.txt")

                # Transcribe the audio and save the result
                result = self.transcribe_audio(audio_path, language)
                self.save_transcription(result["text"], output_file)

    def transcribe_with_timestamps(self, audio_path, language="pt"):
        """
        Transcribes audio and returns segments with timestamps.
        """
        result = self.transcribe_audio(audio_path, language)
        return result["segments"]

    def diarize_audio(self, audio_path):
        """
        Performs speaker diarization on the audio file.
        Returns a list of (segment, speaker) tuples.
        """
        if not self.diarization_pipeline:
            raise ValueError("Speaker diarization not available. Please provide a HuggingFace token.")

        print("Performing speaker diarization...")
        diarization = self.diarization_pipeline(audio_path)

        # Convert to list of tuples with segment info and speaker
        segments = []
        for segment, _, speaker in diarization.itertracks(yield_label=True):
            segments.append({
                'start': segment.start,
                'end': segment.end,
                'speaker': speaker
            })

        return segments

    def combine_transcription_and_diarization(self, transcription_segments, diarization_segments):
        """
        Combines transcription and diarization results to assign speakers to text segments.
        """
        result = []

        for trans_segment in transcription_segments:
            start_time = trans_segment['start']
            end_time = trans_segment['end']
            text = trans_segment['text'].strip()

            # Find the predominant speaker for this time interval
            speakers = []
            for diar_segment in diarization_segments:
                # Check if there's overlap between transcription and diarization segments
                overlap_start = max(start_time, diar_segment['start'])
                overlap_end = min(end_time, diar_segment['end'])

                if overlap_start < overlap_end:  # There is overlap
                    overlap_duration = overlap_end - overlap_start
                    # Weight by overlap duration
                    for _ in range(int(overlap_duration * 10)):  # 10 samples per second
                        speakers.append(diar_segment['speaker'])

            # Choose the most frequent speaker
            if speakers:
                speaker = max(set(speakers), key=speakers.count)
            else:
                speaker = "UNKNOWN"

            result.append({
                'start': start_time,
                'end': end_time,
                'speaker': speaker,
                'text': text
            })

        return result

    def transcribe_with_speaker_identification(self, audio_path, language="pt"):
        """
        Transcribes audio with speaker identification.
        Returns segments with timestamps, text, and speaker labels.
        """
        if not self.diarization_pipeline:
            print("Warning: Speaker diarization not available. Returning transcription without speaker identification.")
            return self.transcribe_with_timestamps(audio_path, language)

        # Get transcription with timestamps
        transcription_segments = self.transcribe_with_timestamps(audio_path, language)

        # Get speaker diarization
        diarization_segments = self.diarize_audio(audio_path)

        # Combine results
        combined_results = self.combine_transcription_and_diarization(
            transcription_segments, diarization_segments
        )

        return combined_results


# Main execution
if __name__ == "__main__":
    # Load environment variables
    load_dotenv()

    # Configuration
    AUDIO_FILE = "/home/<USER>/repositorios/talk-transcription/audio_debate.mp3"  # Change this to your audio file
    MODEL_SIZE = "small"  # Options: tiny, base, small, medium, large
    HF_TOKEN = os.getenv("HF_API_KEY")  # Get from: https://huggingface.co/settings/tokens

    try:
        # Initialize transcriber with optional speaker diarization
        transcriber = AudioTranscriber(model_size=MODEL_SIZE, hf_token=HF_TOKEN)

        # Check if speaker identification is available
        if transcriber.diarization_pipeline:
            print("\n" + "="*60)
            print("TRANSCRIÇÃO COM IDENTIFICAÇÃO DE FALANTES:")
            print("="*60)

            # Transcribe with speaker identification
            segments = transcriber.transcribe_with_speaker_identification(AUDIO_FILE, language="pt")

            # Display results with speaker identification
            for segment in segments:
                start_time = segment['start']
                end_time = segment['end']
                speaker = segment['speaker']
                text = segment['text']
                print(f"\n[{speaker} - {start_time:6.1f}s a {end_time:6.1f}s]:")
                print(f"{text}")

            # Save detailed transcription with speakers
            output_file = "transcription_with_speakers.txt"
            with open(output_file, "w", encoding="utf-8") as f:
                f.write("TRANSCRIÇÃO COM IDENTIFICAÇÃO DE FALANTES\n")
                f.write("="*60 + "\n\n")
                for segment in segments:
                    f.write(f"[{segment['speaker']} - {segment['start']:.1f}s a {segment['end']:.1f}s]:\n")
                    f.write(f"{segment['text']}\n\n")

            print(f"\nTranscription with speaker identification saved to: {output_file}")

        else:
            print("\n" + "="*50)
            print("TRANSCRIÇÃO COM TIMESTAMPS (SEM IDENTIFICAÇÃO DE FALANTES):")
            print("="*50)
            print("Para ativar identificação de falantes, configure HF_API_KEY no arquivo .env")

            # Transcribe with timestamps only
            segments = transcriber.transcribe_with_timestamps(AUDIO_FILE, language="pt")

            # Display transcription with timestamps
            for segment in segments:
                start_time = segment['start']
                end_time = segment['end']
                text = segment['text'].strip()
                print(f"[{start_time:6.1f}s - {end_time:6.1f}s]: {text}")

            # Save basic transcription to file
            full_result = transcriber.transcribe_audio(AUDIO_FILE, language="pt")
            output_file = "transcription.txt"
            transcriber.save_transcription(full_result["text"], output_file)

            print(f"\nBasic transcription saved to: {output_file}")

    except FileNotFoundError as e:
        print(f"Error: {e}")
        print(f"Please make sure the audio file '{AUDIO_FILE}' exists in the current directory.")
    except Exception as e:
        print(f"An error occurred: {e}")
        print("\nTroubleshooting tips:")
        print("1. Make sure you have a valid HuggingFace token in your .env file")
        print("2. Check that all required dependencies are installed")
        print("3. Verify that the audio file exists and is in a supported format")
