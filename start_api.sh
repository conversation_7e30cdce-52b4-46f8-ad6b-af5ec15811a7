#!/bin/bash

# Script para iniciar a API de Transcrição de Áudio

echo "🚀 Iniciando API de Transcrição de Áudio..."

# Verifica se o Python está instalado
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 não encontrado. Por favor, instale o Python 3.7 ou superior."
    exit 1
fi

# Verifica se as dependências estão instaladas
echo "📦 Verificando dependências..."
if ! python3 -c "import fastapi, uvicorn, whisper" &> /dev/null; then
    echo "⚠️  Algumas dependências não estão instaladas."
    echo "📥 Para instalar as dependências, execute:"
    echo "   pip install -r requirements.txt"
    echo ""
    echo "⚠️  Se você estiver em um ambiente gerenciado externamente (como Ubuntu 24+):"
    echo "   1. Crie um ambiente virtual: python3 -m venv api_venv"
    echo "   2. Ative o ambiente: source api_venv/bin/activate"
    echo "   3. Instale as dependências: pip install -r requirements.txt"
    echo "   4. Execute a API: python api.py"
    echo ""
    echo "❌ Parando execução. Instale as dependências primeiro."
    exit 1
fi

# Verifica se o arquivo .env existe
if [ ! -f ".env" ]; then
    echo "⚠️  Arquivo .env não encontrado."
    echo "📝 Copiando .env.example para .env..."
    cp .env.example .env
    echo "✏️  Por favor, edite o arquivo .env com suas configurações antes de continuar."
    echo "   Especialmente o HF_API_KEY se você quiser usar identificação de falantes."
fi

echo "✅ Dependências verificadas!"
echo "🌐 Iniciando servidor FastAPI..."
echo "📍 A API estará disponível em: http://localhost:8000"
echo "📚 Documentação interativa em: http://localhost:8000/docs"
echo ""
echo "Para parar o servidor, pressione Ctrl+C"
echo ""

# Inicia a API
python3 api.py
