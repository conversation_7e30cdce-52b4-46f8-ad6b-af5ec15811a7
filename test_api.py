#!/usr/bin/env python3
"""
Script simples para testar a API de transcrição
"""

import requests
import sys
import os

def test_api():
    """Testa se a API está funcionando"""
    base_url = "http://localhost:8000"
    
    print("🔍 Testando conexão com a API...")
    
    try:
        # Teste de health check
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ API está funcionando!")
            print(f"   - Modelo Whisper: {health_data.get('whisper_model', 'N/A')}")
            print(f"   - Identificação de falantes: {'✅' if health_data.get('speaker_diarization') else '❌'}")
            return True
        else:
            print(f"❌ API retornou status {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Não foi possível conectar à API")
        print("   Certifique-se de que a API está rodando em http://localhost:8000")
        print("   Execute: python api.py")
        return False
    except Exception as e:
        print(f"❌ Erro ao testar API: {e}")
        return False

def test_transcription(audio_file):
    """Testa a transcrição de um arquivo de áudio"""
    base_url = "http://localhost:8000"
    
    if not os.path.exists(audio_file):
        print(f"❌ Arquivo de áudio não encontrado: {audio_file}")
        return False
    
    print(f"🎵 Testando transcrição do arquivo: {audio_file}")
    
    try:
        with open(audio_file, "rb") as f:
            files = {"file": f}
            data = {"language": "pt"}
            
            print("   Enviando arquivo para transcrição...")
            response = requests.post(f"{base_url}/transcribe", files=files, data=data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Transcrição realizada com sucesso!")
                print(f"   Texto (primeiros 100 caracteres): {result['text'][:100]}...")
                return True
            else:
                print(f"❌ Erro na transcrição: {response.status_code}")
                print(f"   Detalhes: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Erro ao testar transcrição: {e}")
        return False

def main():
    """Função principal"""
    print("🚀 Teste da API de Transcrição de Áudio")
    print("=" * 50)
    
    # Teste básico da API
    if not test_api():
        print("\n❌ Falha no teste básico da API")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # Teste de transcrição se houver arquivo de áudio
    audio_file = "audio_debate.mp3"
    if os.path.exists(audio_file):
        if test_transcription(audio_file):
            print("\n✅ Todos os testes passaram!")
        else:
            print("\n❌ Falha no teste de transcrição")
            sys.exit(1)
    else:
        print(f"ℹ️  Arquivo de áudio '{audio_file}' não encontrado")
        print("   Para testar a transcrição, coloque um arquivo de áudio no diretório atual")
    
    print("\n🎉 Testes concluídos!")
    print("\nPara usar a API:")
    print("1. Acesse http://localhost:8000/docs para ver a documentação")
    print("2. Use o script example_client.py para exemplos de uso")
    print("3. Consulte o README_API.md para instruções detalhadas")

if __name__ == "__main__":
    main()
